#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_template_output():
    """验证按模板结构生成的文件"""
    print("=== 验证按模板结构生成的文件 ===")
    
    try:
        # 读取按模板生成的文件
        df_template_output = pd.read_excel("按模板结构-2025年1月居间结算单明细.xlsx")
        print(f"模板输出文件读取成功: {df_template_output.shape}")
        
        # 读取原始模板作为对比
        df_original_template = pd.read_excel("副本模板-2025年1月居间结算单明细(1).xlsx", engine='xlrd')
        print(f"原始模板文件: {df_original_template.shape}")
        
        # 对比列结构
        print(f"\n列结构对比:")
        print(f"原始模板列名: {list(df_original_template.columns)}")
        print(f"生成文件列名: {list(df_template_output.columns)}")
        
        columns_match = list(df_original_template.columns) == list(df_template_output.columns)
        print(f"列结构匹配: {'✅ 是' if columns_match else '❌ 否'}")
        
        # 显示生成文件的前5行
        print(f"\n生成文件前5行数据:")
        for i, row in df_template_output.head(5).iterrows():
            print(f"第{i+1}行:")
            print(f"  居间人/单位: {row['居间人/单位']}")
            print(f"  序号: {row['序号']}")
            print(f"  企业用户名: {row['企业用户名']}")
            print(f"  结算电量（兆瓦时）: {row['结算电量（兆瓦时）']}")
            print(f"  单价（厘）: {row['单价（厘）']}")
            print(f"  税前费用: {row['税前费用']}")
            print(f"  税后合计（元）: {row['税后合计（元）']}")
            print(f"  开户行: {row['开户行']}")
            print(f"  收款人/单位: {row['收款人/单位']}")
            print(f"  银行账号: {row['银行账号']}")
            print()
        
        # 统计信息
        print(f"统计信息:")
        print(f"总记录数: {len(df_template_output)}")
        print(f"总结算电量: {df_template_output['结算电量（兆瓦时）'].sum():.3f} MWh")
        print(f"税前合计: {df_template_output['税前合计（元）'].sum():.2f} 元")
        print(f"税后合计: {df_template_output['税后合计（元）'].sum():.2f} 元")
        
        # 检查数据完整性
        print(f"\n数据完整性检查:")
        for col in df_template_output.columns:
            null_count = df_template_output[col].isnull().sum()
            empty_count = (df_template_output[col] == '').sum() if df_template_output[col].dtype == 'object' else 0
            total_missing = null_count + empty_count
            if total_missing > 0:
                print(f"  {col}: {total_missing} 个缺失值 (空值: {null_count}, 空字符串: {empty_count})")
        
        # 检查联系信息匹配情况
        has_contact = df_template_output[
            (df_template_output['居间人/单位'].notna()) & 
            (df_template_output['居间人/单位'] != '')
        ].shape[0]
        
        has_bank = df_template_output[
            (df_template_output['开户行'].notna()) & 
            (df_template_output['开户行'] != '')
        ].shape[0]
        
        has_account = df_template_output[
            (df_template_output['银行账号'].notna()) & 
            (df_template_output['银行账号'] != '')
        ].shape[0]
        
        print(f"\n联系信息匹配情况:")
        print(f"有居间人信息: {has_contact}/{len(df_template_output)} ({has_contact/len(df_template_output)*100:.1f}%)")
        print(f"有开户行信息: {has_bank}/{len(df_template_output)} ({has_bank/len(df_template_output)*100:.1f}%)")
        print(f"有银行账号信息: {has_account}/{len(df_template_output)} ({has_account/len(df_template_output)*100:.1f}%)")
        
        # 显示费率分布
        print(f"\n费率分布:")
        rate_counts = df_template_output['单价（厘）'].value_counts()
        for rate, count in rate_counts.items():
            print(f"  {rate}厘: {count} 条记录")
        
        # 显示税后费用最高的企业
        print(f"\n税后费用最高的10家企业:")
        top_companies = df_template_output.nlargest(10, '税后合计（元）')
        for i, (_, row) in enumerate(top_companies.iterrows(), 1):
            print(f"  {i}. {row['企业用户名']}: {row['税后合计（元）']:.2f}元")
        
        # 显示有完整信息的样本
        complete_info = df_template_output[
            (df_template_output['居间人/单位'].notna()) & 
            (df_template_output['开户行'].notna()) & 
            (df_template_output['银行账号'].notna()) &
            (df_template_output['居间人/单位'] != '') &
            (df_template_output['开户行'] != '') &
            (df_template_output['银行账号'] != '')
        ]
        
        print(f"\n有完整信息的记录样本 ({len(complete_info)} 条):")
        for i, (_, row) in enumerate(complete_info.head(3).iterrows(), 1):
            print(f"  {i}. {row['企业用户名']}")
            print(f"     居间人: {row['居间人/单位']}")
            print(f"     开户行: {row['开户行']}")
            print(f"     账号: {row['银行账号']}")
            print()
        
    except Exception as e:
        print(f"验证过程出错: {e}")

def compare_with_source():
    """与源数据对比验证"""
    print("=== 与源数据对比验证 ===")
    
    try:
        # 读取源数据和模板输出
        df_source = pd.read_excel("零售套餐用户量费明细 (7).xlsx")
        df_template_output = pd.read_excel("按模板结构-2025年1月居间结算单明细.xlsx")
        
        print(f"源数据记录数: {len(df_source)}")
        print(f"模板输出记录数: {len(df_template_output)}")
        
        # 对比总电量（注意单位转换）
        source_total_kwh = df_source['结算电量（总）'].sum()
        output_total_mwh = df_template_output['结算电量（兆瓦时）'].sum()
        output_total_kwh = output_total_mwh * 1000
        
        print(f"\n电量对比:")
        print(f"源数据总电量: {source_total_kwh:,.0f} kWh")
        print(f"输出数据总电量: {output_total_kwh:,.0f} kWh (转换自 {output_total_mwh:,.3f} MWh)")
        print(f"差异: {abs(source_total_kwh - output_total_kwh):,.0f} kWh")
        
        # 对比交易电费
        source_total_fee = df_source['交易电费'].sum()
        # 根据居间费率反推交易电费
        estimated_trading_fee = df_template_output['税前费用'].sum() / 0.003  # 假设平均费率3厘
        
        print(f"\n交易电费对比:")
        print(f"源数据总交易电费: {source_total_fee:,.2f} 元")
        print(f"估算交易电费: {estimated_trading_fee:,.2f} 元")
        
        # 检查企业名称匹配
        source_companies = set(df_source['用户名称'].dropna())
        output_companies = set(df_template_output['企业用户名'].dropna())
        
        print(f"\n企业名称匹配:")
        print(f"源数据企业数: {len(source_companies)}")
        print(f"输出数据企业数: {len(output_companies)}")
        print(f"完全匹配: {len(source_companies & output_companies)}")
        
    except Exception as e:
        print(f"对比过程出错: {e}")

if __name__ == "__main__":
    verify_template_output()
    compare_with_source()
