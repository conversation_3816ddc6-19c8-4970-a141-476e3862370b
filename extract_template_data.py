#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

def analyze_template_data():
    """详细分析模板文件中的数据"""
    print("=== 分析模板文件数据 ===")
    
    # 读取模板文件
    df_template = pd.read_excel("副本模板-2025年1月居间结算单明细(1).xlsx", engine='xlrd')
    print(f"模板数据: {df_template.shape}")
    print(f"列名: {list(df_template.columns)}")
    
    # 显示所有数据
    print(f"\n模板中的所有数据:")
    pd.set_option('display.max_columns', None)
    pd.set_option('display.max_rows', 20)
    pd.set_option('display.width', None)
    print(df_template.head(20))
    
    # 分析各字段的唯一值
    print(f"\n各字段唯一值分析:")
    
    # 居间人/单位
    intermediaries = df_template['居间人/单位'].dropna().unique()
    print(f"居间人/单位 ({len(intermediaries)}个): {intermediaries}")
    
    # 发票类型
    invoice_types = df_template['发票'].dropna().unique()
    print(f"发票类型 ({len(invoice_types)}个): {invoice_types}")
    
    # 开户行
    banks = df_template['开户行'].dropna().unique()
    print(f"开户行 ({len(banks)}个): {banks[:10]}...")  # 只显示前10个
    
    # 收款人/单位
    payees = df_template['收款人/单位'].dropna().unique()
    print(f"收款人/单位 ({len(payees)}个): {payees}")
    
    # 银行账号
    accounts = df_template['银行账号'].dropna().unique()
    print(f"银行账号 ({len(accounts)}个): {accounts[:5]}...")  # 只显示前5个
    
    # 分析居间人与收款信息的对应关系
    print(f"\n居间人与收款信息对应关系:")
    intermediary_info = df_template.groupby('居间人/单位').agg({
        '发票': lambda x: x.mode().iloc[0] if not x.mode().empty else np.nan,
        '开户行': lambda x: x.mode().iloc[0] if not x.mode().empty else np.nan,
        '收款人/单位': lambda x: x.mode().iloc[0] if not x.mode().empty else np.nan,
        '银行账号': lambda x: x.mode().iloc[0] if not x.mode().empty else np.nan
    }).reset_index()
    
    print(intermediary_info)
    
    return df_template, intermediary_info

def create_intermediary_mapping():
    """创建居间人信息映射"""
    print(f"\n=== 创建居间人信息映射 ===")
    
    df_template, intermediary_info = analyze_template_data()
    
    # 创建映射字典
    intermediary_map = {}
    for _, row in intermediary_info.iterrows():
        intermediary_name = row['居间人/单位']
        if pd.notna(intermediary_name):
            intermediary_map[intermediary_name] = {
                '发票': row['发票'] if pd.notna(row['发票']) else '普通发票',
                '开户行': row['开户行'] if pd.notna(row['开户行']) else '',
                '收款人/单位': row['收款人/单位'] if pd.notna(row['收款人/单位']) else intermediary_name,
                '银行账号': str(row['银行账号']).replace('.0', '') if pd.notna(row['银行账号']) else ''
            }
    
    print(f"创建了 {len(intermediary_map)} 个居间人映射:")
    for name, info in intermediary_map.items():
        print(f"  {name}:")
        print(f"    发票: {info['发票']}")
        print(f"    开户行: {info['开户行'][:50]}..." if len(str(info['开户行'])) > 50 else f"    开户行: {info['开户行']}")
        print(f"    收款人: {info['收款人/单位']}")
        print(f"    账号: {info['银行账号']}")
        print()
    
    return intermediary_map

def match_companies_to_intermediaries():
    """将企业匹配到居间人"""
    print(f"=== 企业与居间人匹配 ===")
    
    # 读取源数据
    df_source = pd.read_excel("零售套餐用户量费明细 (7).xlsx")
    
    # 读取工作簿3中的相关信息
    df_business = pd.read_excel("工作簿3.xlsx", sheet_name='商务居间')
    df_settlement = pd.read_excel("工作簿3.xlsx", sheet_name='浙江绿电结算信息')
    
    # 创建企业到居间人的映射
    company_to_intermediary = {}
    
    # 从商务居间表中提取映射关系
    for _, row in df_business.iterrows():
        company = row.get('企业名称/个人名称')
        contact = row.get('联系人')
        if pd.notna(company) and pd.notna(contact):
            company_to_intermediary[company] = contact
    
    # 从浙江绿电结算信息中提取映射关系
    for _, row in df_settlement.iterrows():
        company = row.get('企业名称')
        contact = row.get('联系人')
        if pd.notna(company) and pd.notna(contact):
            company_to_intermediary[company] = contact
    
    print(f"找到 {len(company_to_intermediary)} 个企业与居间人的映射关系")
    
    # 显示一些映射关系
    print("部分映射关系:")
    for i, (company, intermediary) in enumerate(list(company_to_intermediary.items())[:10]):
        print(f"  {company} -> {intermediary}")
    
    return company_to_intermediary

if __name__ == "__main__":
    # 分析模板数据
    intermediary_map = create_intermediary_mapping()
    
    # 创建企业映射
    company_to_intermediary = match_companies_to_intermediaries()
    
    print(f"\n=== 总结 ===")
    print(f"模板中有 {len(intermediary_map)} 个居间人信息")
    print(f"找到 {len(company_to_intermediary)} 个企业与居间人的映射关系")
