#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import re
import numpy as np
from fuzzywuzzy import fuzz

def load_template_intermediary_data():
    """从模板中加载居间人信息"""
    print("=== 从模板加载居间人信息 ===")
    
    # 读取模板文件
    df_template = pd.read_excel("副本模板-2025年1月居间结算单明细(1).xlsx", engine='xlrd')
    
    # 创建居间人信息映射
    intermediary_info = df_template.groupby('居间人/单位').agg({
        '发票': lambda x: x.mode().iloc[0] if not x.mode().empty else '普通发票',
        '开户行': lambda x: x.mode().iloc[0] if not x.mode().empty else '',
        '收款人/单位': lambda x: x.mode().iloc[0] if not x.mode().empty else '',
        '银行账号': lambda x: x.mode().iloc[0] if not x.mode().empty else ''
    }).reset_index()
    
    # 转换为字典
    intermediary_map = {}
    for _, row in intermediary_info.iterrows():
        intermediary_name = row['居间人/单位']
        if pd.notna(intermediary_name):
            intermediary_map[intermediary_name] = {
                '发票': row['发票'] if pd.notna(row['发票']) else '普通发票',
                '开户行': row['开户行'] if pd.notna(row['开户行']) else '',
                '收款人/单位': row['收款人/单位'] if pd.notna(row['收款人/单位']) else intermediary_name,
                '银行账号': format_bank_account(row['银行账号']) if pd.notna(row['银行账号']) else ''
            }
    
    print(f"从模板中提取了 {len(intermediary_map)} 个居间人信息")
    return intermediary_map

def format_bank_account(account):
    """格式化银行账号"""
    if pd.isna(account):
        return ''
    
    # 如果是科学计数法，转换为正常格式
    if isinstance(account, float):
        if account > 1e10:  # 大数字，可能是银行账号
            return f"{int(account)}"
        else:
            return str(account).replace('.0', '')
    
    return str(account).replace('.0', '')

def create_company_intermediary_mapping():
    """创建企业到居间人的映射"""
    print("=== 创建企业到居间人映射 ===")
    
    # 读取工作簿3中的数据
    df_business = pd.read_excel("工作簿3.xlsx", sheet_name='商务居间')
    df_settlement = pd.read_excel("工作簿3.xlsx", sheet_name='浙江绿电结算信息')
    
    company_to_intermediary = {}
    
    # 从商务居间表中提取映射关系
    for _, row in df_business.iterrows():
        company = row.get('企业名称/个人名称')
        contact = row.get('联系人')
        if pd.notna(company) and pd.notna(contact):
            company_to_intermediary[company] = contact
    
    # 从浙江绿电结算信息中提取映射关系
    for _, row in df_settlement.iterrows():
        company = row.get('企业名称')
        contact = row.get('联系人')
        if pd.notna(company) and pd.notna(contact):
            company_to_intermediary[company] = contact
    
    print(f"创建了 {len(company_to_intermediary)} 个企业到居间人的映射")
    return company_to_intermediary

def find_best_intermediary_match(company_name, intermediary_map, company_to_intermediary):
    """为企业找到最佳的居间人匹配"""
    
    # 1. 直接匹配
    if company_name in company_to_intermediary:
        intermediary_name = company_to_intermediary[company_name]
        if intermediary_name in intermediary_map:
            return intermediary_name, intermediary_map[intermediary_name]
    
    # 2. 模糊匹配企业名称
    best_match = None
    best_score = 0
    
    for mapped_company, intermediary_name in company_to_intermediary.items():
        if pd.isna(mapped_company) or pd.isna(intermediary_name):
            continue
        
        score = fuzz.ratio(str(company_name), str(mapped_company))
        if score > best_score and score >= 80:  # 80%相似度阈值
            best_score = score
            if intermediary_name in intermediary_map:
                best_match = (intermediary_name, intermediary_map[intermediary_name])
    
    if best_match:
        return best_match
    
    # 3. 如果没有匹配到，使用默认居间人（选择最常用的）
    # 从模板中找到最常用的居间人
    default_intermediary = '任佳佳'  # 根据模板数据，任佳佳是最常用的
    if default_intermediary in intermediary_map:
        return default_intermediary, intermediary_map[default_intermediary]
    
    # 4. 如果连默认的都没有，返回空信息
    return None, {
        '发票': '普通发票',
        '开户行': '',
        '收款人/单位': '',
        '银行账号': ''
    }

def create_complete_settlement_report():
    """创建完整的居间结算单"""
    print("\n=== 创建完整的居间结算单 ===")
    
    # 加载所有必要数据
    intermediary_map = load_template_intermediary_data()
    company_to_intermediary = create_company_intermediary_mapping()
    
    # 读取源数据
    df_source = pd.read_excel("零售套餐用户量费明细 (7).xlsx")
    print(f"源数据: {df_source.shape}")
    
    # 创建结果数据
    result_data = []
    
    print("开始数据转换...")
    for idx, source_row in df_source.iterrows():
        company_name = source_row['用户名称']
        settlement_amount_kwh = float(source_row['结算电量（总）']) if pd.notna(source_row['结算电量（总）']) else 0
        settlement_amount_mwh = settlement_amount_kwh / 1000  # 转换为兆瓦时
        trading_fee = float(source_row['交易电费']) if pd.notna(source_row['交易电费']) else 0
        
        # 查找最佳居间人匹配
        intermediary_name, intermediary_info = find_best_intermediary_match(
            company_name, intermediary_map, company_to_intermediary
        )
        
        # 计算费用（默认3厘）
        rate = 0.003  # 3厘
        settlement_fee_before_tax = trading_fee * rate
        
        # 根据发票类型计算税后费用
        invoice_type = intermediary_info.get('发票', '普通发票')
        if '无发票' in invoice_type:
            # 无发票，扣13个点
            settlement_fee_after_tax = settlement_fee_before_tax * 0.87
        elif '开6个点' in invoice_type:
            # 开6个点发票，抵6个点，扣7个点
            settlement_fee_after_tax = settlement_fee_before_tax * 0.93
        elif '开1个点' in invoice_type:
            # 开1个点发票，抵1个点，扣12个点
            settlement_fee_after_tax = settlement_fee_before_tax * 0.88
        else:
            # 默认情况，按6%增值税计算
            settlement_fee_after_tax = settlement_fee_before_tax * 1.06
        
        # 创建行数据
        row_data = {
            '居间人/单位': intermediary_name if intermediary_name else '',
            '序号': idx + 1,
            '企业用户名': company_name,
            '结算电量（兆瓦时）': round(settlement_amount_mwh, 3),
            '单价（厘）': rate * 1000,  # 转换为厘
            '税前费用': round(settlement_fee_before_tax, 2),
            '税前合计（元）': round(settlement_fee_before_tax, 2),
            '发票': invoice_type,
            '税后合计（元）': round(settlement_fee_after_tax, 2),
            '开户行': intermediary_info.get('开户行', ''),
            '收款人/单位': intermediary_info.get('收款人/单位', ''),
            '银行账号': intermediary_info.get('银行账号', '')
        }
        
        result_data.append(row_data)
    
    # 创建DataFrame
    df_result = pd.DataFrame(result_data)
    
    # 创建Excel文件
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "2025年1月居间结算单明细"
    
    # 设置列宽
    column_widths = [15, 8, 30, 15, 12, 15, 15, 25, 15, 35, 25, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width
    
    # 写入数据
    for r in dataframe_to_rows(df_result, index=False, header=True):
        ws.append(r)
    
    # 设置样式
    header_font = Font(name='宋体', size=11, bold=True)
    header_alignment = Alignment(horizontal='center', vertical='center')
    header_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    # 应用表头样式
    for col in range(1, len(df_result.columns) + 1):
        cell = ws.cell(row=1, column=col)
        cell.font = header_font
        cell.alignment = header_alignment
        cell.fill = header_fill
        cell.border = thin_border
    
    # 设置数据行样式
    for row in range(2, len(df_result) + 2):
        for col in range(1, len(df_result.columns) + 1):
            cell = ws.cell(row=row, column=col)
            cell.border = thin_border
            
            # 根据列类型设置对齐方式
            if col in [2]:  # 序号居中
                cell.alignment = Alignment(horizontal='center')
            elif col in [4, 5, 6, 7, 9]:  # 数值列右对齐
                cell.alignment = Alignment(horizontal='right')
            else:
                cell.alignment = Alignment(horizontal='left')
    
    # 添加合计行
    total_row = len(df_result) + 2
    ws.cell(row=total_row, column=1, value="合计")
    ws.cell(row=total_row, column=6, value=df_result['税前费用'].sum())
    ws.cell(row=total_row, column=7, value=df_result['税前合计（元）'].sum())
    ws.cell(row=total_row, column=9, value=df_result['税后合计（元）'].sum())
    
    # 设置合计行样式
    for col in range(1, len(df_result.columns) + 1):
        cell = ws.cell(row=total_row, column=col)
        cell.font = Font(bold=True)
        cell.border = thin_border
        cell.fill = PatternFill(start_color='E6E6FA', end_color='E6E6FA', fill_type='solid')
        if col == 1:
            cell.alignment = Alignment(horizontal='center')
        elif col in [6, 7, 9]:
            cell.alignment = Alignment(horizontal='right')
    
    # 保存文件
    output_file = "完整模板结构-2025年1月居间结算单明细.xlsx"
    wb.save(output_file)
    
    # 统计信息
    matched_intermediaries = df_result[df_result['居间人/单位'] != ''].shape[0]
    matched_banks = df_result[df_result['开户行'] != ''].shape[0]
    matched_accounts = df_result[df_result['银行账号'] != ''].shape[0]
    
    print(f"\n转换完成！")
    print(f"输出文件: {output_file}")
    print(f"处理记录数: {len(df_result)}")
    print(f"匹配到居间人: {matched_intermediaries}/{len(df_result)} ({matched_intermediaries/len(df_result)*100:.1f}%)")
    print(f"匹配到开户行: {matched_banks}/{len(df_result)} ({matched_banks/len(df_result)*100:.1f}%)")
    print(f"匹配到银行账号: {matched_accounts}/{len(df_result)} ({matched_accounts/len(df_result)*100:.1f}%)")
    print(f"税前合计: {df_result['税前合计（元）'].sum():.2f}元")
    print(f"税后合计: {df_result['税后合计（元）'].sum():.2f}元")
    
    return output_file, df_result

if __name__ == "__main__":
    create_complete_settlement_report()
