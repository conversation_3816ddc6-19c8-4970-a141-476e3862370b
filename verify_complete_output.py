#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_complete_output():
    """验证完整版本的输出"""
    print("=== 验证完整版本输出 ===")
    
    try:
        # 读取完整版本输出
        df_complete = pd.read_excel("完整模板结构-2025年1月居间结算单明细.xlsx")
        print(f"完整版本读取成功: {df_complete.shape}")
        
        # 读取原始模板作为对比
        df_template = pd.read_excel("副本模板-2025年1月居间结算单明细(1).xlsx", engine='xlrd')
        print(f"原始模板: {df_template.shape}")
        
        # 验证列结构
        print(f"\n列结构验证:")
        template_columns = list(df_template.columns)
        output_columns = list(df_complete.columns)
        
        print(f"模板列名: {template_columns}")
        print(f"输出列名: {output_columns}")
        print(f"列结构匹配: {'✅ 完全匹配' if template_columns == output_columns else '❌ 不匹配'}")
        
        # 显示前5行数据
        print(f"\n前5行数据展示:")
        for i, row in df_complete.head(5).iterrows():
            print(f"第{i+1}行:")
            print(f"  居间人/单位: {row['居间人/单位']}")
            print(f"  企业用户名: {row['企业用户名']}")
            print(f"  结算电量（兆瓦时）: {row['结算电量（兆瓦时）']}")
            print(f"  单价（厘）: {row['单价（厘）']}")
            print(f"  税前费用: {row['税前费用']}")
            print(f"  发票: {row['发票']}")
            print(f"  税后合计（元）: {row['税后合计（元）']}")
            print(f"  开户行: {row['开户行'][:30]}..." if len(str(row['开户行'])) > 30 else f"  开户行: {row['开户行']}")
            print(f"  收款人/单位: {row['收款人/单位']}")
            print(f"  银行账号: {row['银行账号']}")
            print()
        
        # 统计信息
        print(f"统计信息:")
        print(f"总记录数: {len(df_complete)}")
        print(f"总结算电量: {df_complete['结算电量（兆瓦时）'].sum():.3f} MWh")
        print(f"税前合计: {df_complete['税前合计（元）'].sum():.2f} 元")
        print(f"税后合计: {df_complete['税后合计（元）'].sum():.2f} 元")
        
        # 居间人分布
        print(f"\n居间人分布:")
        intermediary_counts = df_complete['居间人/单位'].value_counts()
        for intermediary, count in intermediary_counts.head(10).items():
            print(f"  {intermediary}: {count} 条记录")
        
        # 发票类型分布
        print(f"\n发票类型分布:")
        invoice_counts = df_complete['发票'].value_counts()
        for invoice_type, count in invoice_counts.items():
            print(f"  {invoice_type}: {count} 条记录")
        
        # 开户行分布
        print(f"\n开户行分布:")
        bank_counts = df_complete['开户行'].value_counts()
        for bank, count in bank_counts.head(5).items():
            bank_display = bank[:40] + "..." if len(str(bank)) > 40 else bank
            print(f"  {bank_display}: {count} 条记录")
        
        # 数据完整性检查
        print(f"\n数据完整性检查:")
        for col in df_complete.columns:
            null_count = df_complete[col].isnull().sum()
            empty_count = (df_complete[col] == '').sum() if df_complete[col].dtype == 'object' else 0
            total_missing = null_count + empty_count
            if total_missing > 0:
                print(f"  {col}: {total_missing} 个缺失值")
            else:
                print(f"  {col}: ✅ 完整")
        
        # 显示税后费用最高的企业
        print(f"\n税后费用最高的10家企业:")
        top_companies = df_complete.nlargest(10, '税后合计（元）')
        for i, (_, row) in enumerate(top_companies.iterrows(), 1):
            print(f"  {i}. {row['企业用户名']}: {row['税后合计（元）']:.2f}元 (居间人: {row['居间人/单位']})")
        
        # 显示不同居间人的样本记录
        print(f"\n不同居间人的样本记录:")
        unique_intermediaries = df_complete['居间人/单位'].unique()[:5]
        for intermediary in unique_intermediaries:
            sample = df_complete[df_complete['居间人/单位'] == intermediary].iloc[0]
            print(f"  居间人: {intermediary}")
            print(f"    样本企业: {sample['企业用户名']}")
            print(f"    发票类型: {sample['发票']}")
            print(f"    开户行: {sample['开户行'][:30]}..." if len(str(sample['开户行'])) > 30 else f"    开户行: {sample['开户行']}")
            print(f"    收款人: {sample['收款人/单位']}")
            print()
        
    except Exception as e:
        print(f"验证过程出错: {e}")

def compare_with_template():
    """与原始模板对比"""
    print("=== 与原始模板对比 ===")
    
    try:
        df_template = pd.read_excel("副本模板-2025年1月居间结算单明细(1).xlsx", engine='xlrd')
        df_complete = pd.read_excel("完整模板结构-2025年1月居间结算单明细.xlsx")
        
        print(f"原始模板记录数: {len(df_template)}")
        print(f"生成文件记录数: {len(df_complete)}")
        
        # 对比居间人分布
        print(f"\n居间人对比:")
        template_intermediaries = set(df_template['居间人/单位'].dropna())
        output_intermediaries = set(df_complete['居间人/单位'].dropna())
        
        print(f"模板中的居间人 ({len(template_intermediaries)}个): {sorted(template_intermediaries)}")
        print(f"输出中的居间人 ({len(output_intermediaries)}个): {sorted(output_intermediaries)}")
        
        common_intermediaries = template_intermediaries & output_intermediaries
        print(f"共同的居间人 ({len(common_intermediaries)}个): {sorted(common_intermediaries)}")
        
        # 对比发票类型
        print(f"\n发票类型对比:")
        template_invoices = set(df_template['发票'].dropna())
        output_invoices = set(df_complete['发票'].dropna())
        
        print(f"模板中的发票类型: {template_invoices}")
        print(f"输出中的发票类型: {output_invoices}")
        
        # 对比开户行
        print(f"\n开户行对比:")
        template_banks = set(df_template['开户行'].dropna())
        output_banks = set(df_complete['开户行'].dropna())
        
        print(f"模板中的开户行数量: {len(template_banks)}")
        print(f"输出中的开户行数量: {len(output_banks)}")
        print(f"共同的开户行数量: {len(template_banks & output_banks)}")
        
    except Exception as e:
        print(f"对比过程出错: {e}")

if __name__ == "__main__":
    verify_complete_output()
    compare_with_template()
