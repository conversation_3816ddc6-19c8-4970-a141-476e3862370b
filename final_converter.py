#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import re
from fuzzywuzzy import fuzz

def fuzzy_match_company(company_name, reference_companies, threshold=80):
    """使用模糊匹配查找最相似的公司名称"""
    if pd.isna(company_name):
        return None
    
    best_match = None
    best_score = 0
    
    for ref_company in reference_companies:
        if pd.isna(ref_company):
            continue
        score = fuzz.ratio(str(company_name), str(ref_company))
        if score > best_score and score >= threshold:
            best_score = score
            best_match = ref_company
    
    return best_match

def create_final_settlement_report():
    """创建最终的居间结算单明细"""
    print("=== 创建最终居间结算单明细 ===")
    
    # 加载数据
    df_source = pd.read_excel("零售套餐用户量费明细 (7).xlsx")
    df_business = pd.read_excel("工作簿3.xlsx", sheet_name='商务居间')
    df_settlement = pd.read_excel("工作簿3.xlsx", sheet_name='浙江绿电结算信息')
    df_company = pd.read_excel("工作簿3.xlsx", sheet_name='耀辉唐电售电公司业务')
    
    print(f"源数据: {df_source.shape}")
    print(f"商务居间: {df_business.shape}")
    print(f"绿电结算: {df_settlement.shape}")
    print(f"售电公司: {df_company.shape}")
    
    # 创建新工作簿
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "2025年1月居间结算单明细"
    
    # 设置列宽
    column_widths = [6, 30, 18, 20, 15, 15, 10, 15, 12, 15, 25, 30, 20]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width
    
    # 创建表头
    headers = [
        "序号", "企业名称", "用户编号", "供电单位", "结算电量(kWh)", 
        "交易电费(元)", "居间费率", "居间费用(元)", "联系人", "联系电话",
        "开户行", "银行账号", "备注"
    ]
    
    # 设置表头样式
    header_font = Font(name='宋体', size=11, bold=True)
    header_alignment = Alignment(horizontal='center', vertical='center')
    header_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.alignment = header_alignment
        cell.fill = header_fill
        cell.border = thin_border
    
    # 创建匹配映射
    print("创建企业信息映射...")
    
    # 绿电结算信息映射
    settlement_companies = df_settlement['企业名称'].dropna().tolist()
    settlement_map = {}
    for _, row in df_settlement.iterrows():
        if pd.notna(row['企业名称']):
            settlement_map[row['企业名称']] = {
                '联系人': row.get('联系人', ''),
                '联系电话': str(row.get('联系人手机', '')).replace('.0', '') if pd.notna(row.get('联系人手机')) else '',
                '开户行': row.get('开户行', ''),
                '银行账号': str(row.get('银行账号', '')).replace('.0', '') if pd.notna(row.get('银行账号')) else '',
                '结算方式': row.get('结算方式', '')
            }
    
    # 商务居间映射
    business_companies = df_business['企业名称/个人名称'].dropna().tolist()
    business_map = {}
    for _, row in df_business.iterrows():
        if pd.notna(row['企业名称/个人名称']):
            price_info = str(row.get('价格', ''))
            rate = extract_rate_from_text(price_info)
            
            business_map[row['企业名称/个人名称']] = {
                '居间费率': rate,
                '联系人': row.get('联系人', ''),
                '联系电话': str(row.get('联系人手机', '')).replace('.0', '') if pd.notna(row.get('联系人手机')) else '',
                '开户行': row.get('开户行', ''),
                '银行账号': str(row.get('银行账号', '')).replace('.0', '') if pd.notna(row.get('银行账号')) else '',
                '结算方式': row.get('结算方式', '')
            }
    
    # 售电公司业务映射
    company_companies = df_company['公司名称'].dropna().tolist()
    company_map = {}
    for _, row in df_company.iterrows():
        if pd.notna(row['公司名称']):
            company_map[row['公司名称']] = {
                '联系人': '',  # 售电公司数据中没有直接的联系人信息
                '联系电话': str(row.get(' 联系电话', '')).replace('.0', '') if pd.notna(row.get(' 联系电话')) else '',
                '居间销售': row.get('居间/销售', ''),
                '用户类型': row.get('用户类型', ''),
                '电压等级': row.get('电压等级（kV）', '')
            }
    
    # 填充数据
    print("开始填充数据...")
    row_num = 2
    total_settlement_fee = 0
    matched_count = 0
    
    for idx, source_row in df_source.iterrows():
        company_name = source_row['用户名称']
        settlement_amount = float(source_row['结算电量（总）']) if pd.notna(source_row['结算电量（总）']) else 0
        trading_fee = float(source_row['交易电费']) if pd.notna(source_row['交易电费']) else 0
        
        # 尝试精确匹配
        settlement_info = settlement_map.get(company_name, {})
        business_info = business_map.get(company_name, {})
        company_info = company_map.get(company_name, {})
        
        # 如果精确匹配失败，尝试模糊匹配
        if not settlement_info and not business_info and not company_info:
            # 模糊匹配绿电结算信息
            fuzzy_settlement = fuzzy_match_company(company_name, settlement_companies)
            if fuzzy_settlement:
                settlement_info = settlement_map.get(fuzzy_settlement, {})
            
            # 模糊匹配商务居间
            fuzzy_business = fuzzy_match_company(company_name, business_companies)
            if fuzzy_business:
                business_info = business_map.get(fuzzy_business, {})
            
            # 模糊匹配售电公司
            fuzzy_company = fuzzy_match_company(company_name, company_companies)
            if fuzzy_company:
                company_info = company_map.get(fuzzy_company, {})
        
        # 确定居间费率
        rate = business_info.get('居间费率', 0.003)  # 默认3厘
        settlement_fee = trading_fee * rate
        total_settlement_fee += settlement_fee
        
        # 优先级：绿电结算信息 > 商务居间 > 售电公司业务
        contact_info = {}
        if settlement_info:
            contact_info = settlement_info
            matched_count += 1
        elif business_info:
            contact_info = business_info
            matched_count += 1
        elif company_info:
            contact_info = company_info
            matched_count += 1
        
        # 填充行数据
        ws.cell(row=row_num, column=1, value=row_num-1)
        ws.cell(row=row_num, column=2, value=company_name)
        ws.cell(row=row_num, column=3, value=int(source_row['用户编号']) if pd.notna(source_row['用户编号']) else '')
        ws.cell(row=row_num, column=4, value=source_row['供电单位'])
        ws.cell(row=row_num, column=5, value=int(settlement_amount))
        ws.cell(row=row_num, column=6, value=round(trading_fee, 2))
        ws.cell(row=row_num, column=7, value=f"{rate*1000:.1f}‰")
        ws.cell(row=row_num, column=8, value=round(settlement_fee, 2))
        ws.cell(row=row_num, column=9, value=contact_info.get('联系人', ''))
        ws.cell(row=row_num, column=10, value=contact_info.get('联系电话', ''))
        ws.cell(row=row_num, column=11, value=contact_info.get('开户行', ''))
        ws.cell(row=row_num, column=12, value=contact_info.get('银行账号', ''))
        ws.cell(row=row_num, column=13, value=contact_info.get('结算方式', ''))
        
        # 设置行样式
        for col in range(1, 14):
            cell = ws.cell(row=row_num, column=col)
            cell.border = thin_border
            if col in [1, 7]:  # 序号和费率居中
                cell.alignment = Alignment(horizontal='center')
            elif col in [5, 6, 8]:  # 数值列右对齐
                cell.alignment = Alignment(horizontal='right')
            else:
                cell.alignment = Alignment(horizontal='left')
        
        row_num += 1
    
    # 添加合计行
    ws.cell(row=row_num, column=1, value="合计")
    ws.cell(row=row_num, column=6, value=round(df_source['交易电费'].sum(), 2))
    ws.cell(row=row_num, column=8, value=round(total_settlement_fee, 2))
    
    # 设置合计行样式
    for col in range(1, 14):
        cell = ws.cell(row=row_num, column=col)
        cell.font = Font(bold=True)
        cell.border = thin_border
        cell.fill = PatternFill(start_color='E6E6FA', end_color='E6E6FA', fill_type='solid')
        if col in [1]:
            cell.alignment = Alignment(horizontal='center')
        elif col in [6, 8]:
            cell.alignment = Alignment(horizontal='right')
    
    # 保存文件
    output_file = "2025年1月居间结算单明细-最终版.xlsx"
    wb.save(output_file)
    
    print(f"\n转换完成！")
    print(f"输出文件: {output_file}")
    print(f"处理记录数: {len(df_source)}")
    print(f"匹配到联系信息的记录: {matched_count}/{len(df_source)}")
    print(f"总交易电费: {df_source['交易电费'].sum():,.2f}元")
    print(f"总居间费用: {total_settlement_fee:.2f}元")
    
    return output_file

def extract_rate_from_text(text):
    """从文本中提取费率"""
    if pd.isna(text) or text == '':
        return 0.003
    
    text = str(text).lower()
    
    # 查找百分比
    percent_match = re.search(r'(\d+(?:\.\d+)?)%', text)
    if percent_match:
        return float(percent_match.group(1)) / 100
    
    # 查找厘
    li_match = re.search(r'(\d+(?:\.\d+)?)厘', text)
    if li_match:
        return float(li_match.group(1)) / 1000
    
    # 查找分成比例
    if '分成' in text:
        ratio_match = re.search(r'(\d+):(\d+)', text)
        if ratio_match:
            a, b = int(ratio_match.group(1)), int(ratio_match.group(2))
            return a / (a + b)
    
    return 0.003

if __name__ == "__main__":
    try:
        create_final_settlement_report()
    except ImportError:
        print("需要安装fuzzywuzzy库来进行模糊匹配")
        print("运行: pip install fuzzywuzzy python-Levenshtein")
        # 不使用模糊匹配的简化版本
        print("使用简化版本...")
        exec(open('enhanced_converter.py').read())
