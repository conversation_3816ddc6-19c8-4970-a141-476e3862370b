#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import re

def load_and_clean_data():
    """加载并清理所有数据"""
    print("=== 加载数据 ===")
    
    # 1. 加载源数据
    df_source = pd.read_excel("零售套餐用户量费明细 (7).xlsx")
    print(f"源数据: {df_source.shape}")
    
    # 2. 加载商务居间数据
    df_business = pd.read_excel("工作簿3.xlsx", sheet_name='商务居间')
    print(f"商务居间数据: {df_business.shape}")
    
    # 3. 加载浙江绿电结算信息
    df_settlement = pd.read_excel("工作簿3.xlsx", sheet_name='浙江绿电结算信息')
    print(f"浙江绿电结算信息: {df_settlement.shape}")
    
    # 4. 加载耀辉唐电售电公司业务数据（可能包含用户信息）
    df_company = pd.read_excel("工作簿3.xlsx", sheet_name='耀辉唐电售电公司业务')
    print(f"售电公司业务数据: {df_company.shape}")
    
    return df_source, df_business, df_settlement, df_company

def create_settlement_report():
    """创建居间结算单明细"""
    print("\n=== 创建居间结算单明细 ===")
    
    # 加载数据
    df_source, df_business, df_settlement, df_company = load_and_clean_data()
    
    # 创建新工作簿
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "2025年1月居间结算单明细"
    
    # 设置列宽
    column_widths = [8, 25, 15, 20, 15, 15, 10, 15, 12, 15, 20, 25, 20]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width
    
    # 创建表头
    headers = [
        "序号", "企业名称", "用户编号", "供电单位", "结算电量(kWh)", 
        "交易电费(元)", "居间费率", "居间费用(元)", "联系人", "联系电话",
        "开户行", "银行账号", "备注"
    ]
    
    # 设置表头样式
    header_font = Font(name='宋体', size=11, bold=True)
    header_alignment = Alignment(horizontal='center', vertical='center')
    header_fill = PatternFill(start_color='E6E6FA', end_color='E6E6FA', fill_type='solid')
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.alignment = header_alignment
        cell.fill = header_fill
    
    # 处理数据匹配
    print("开始数据匹配...")
    
    # 创建企业名称到结算信息的映射
    settlement_map = {}
    for _, row in df_settlement.iterrows():
        if pd.notna(row['企业名称']):
            settlement_map[row['企业名称']] = {
                '联系人': row.get('联系人', ''),
                '联系电话': row.get('联系人手机', ''),
                '开户行': row.get('开户行', ''),
                '银行账号': row.get('银行账号', ''),
                '结算方式': row.get('结算方式', '')
            }
    
    # 创建商务居间费率映射
    business_map = {}
    for _, row in df_business.iterrows():
        if pd.notna(row['企业名称/个人名称']):
            # 提取费率信息
            price_info = str(row.get('价格', ''))
            rate = extract_rate_from_text(price_info)
            
            business_map[row['企业名称/个人名称']] = {
                '居间费率': rate,
                '联系人': row.get('联系人', ''),
                '联系电话': row.get('联系人手机', ''),
                '开户行': row.get('开户行', ''),
                '银行账号': row.get('银行账号', ''),
                '结算方式': row.get('结算方式', '')
            }
    
    # 填充数据
    row_num = 2
    total_settlement_fee = 0
    
    for idx, source_row in df_source.iterrows():
        company_name = source_row['用户名称']
        settlement_amount = float(source_row['结算电量（总）']) if pd.notna(source_row['结算电量（总）']) else 0
        trading_fee = float(source_row['交易电费']) if pd.notna(source_row['交易电费']) else 0
        
        # 查找匹配的结算信息
        settlement_info = settlement_map.get(company_name, {})
        business_info = business_map.get(company_name, {})
        
        # 确定居间费率
        rate = business_info.get('居间费率', 0.003)  # 默认3厘
        settlement_fee = trading_fee * rate
        total_settlement_fee += settlement_fee
        
        # 填充行数据
        ws.cell(row=row_num, column=1, value=row_num-1)  # 序号
        ws.cell(row=row_num, column=2, value=company_name)  # 企业名称
        ws.cell(row=row_num, column=3, value=source_row['用户编号'])  # 用户编号
        ws.cell(row=row_num, column=4, value=source_row['供电单位'])  # 供电单位
        ws.cell(row=row_num, column=5, value=settlement_amount)  # 结算电量
        ws.cell(row=row_num, column=6, value=trading_fee)  # 交易电费
        ws.cell(row=row_num, column=7, value=f"{rate*1000:.1f}‰")  # 居间费率
        ws.cell(row=row_num, column=8, value=round(settlement_fee, 2))  # 居间费用
        
        # 联系信息优先从结算信息中获取，其次从商务居间
        contact_info = settlement_info if settlement_info else business_info
        ws.cell(row=row_num, column=9, value=contact_info.get('联系人', ''))  # 联系人
        ws.cell(row=row_num, column=10, value=contact_info.get('联系电话', ''))  # 联系电话
        ws.cell(row=row_num, column=11, value=contact_info.get('开户行', ''))  # 开户行
        ws.cell(row=row_num, column=12, value=contact_info.get('银行账号', ''))  # 银行账号
        ws.cell(row=row_num, column=13, value=contact_info.get('结算方式', ''))  # 备注
        
        row_num += 1
    
    # 添加合计行
    ws.cell(row=row_num, column=1, value="合计")
    ws.cell(row=row_num, column=8, value=round(total_settlement_fee, 2))
    
    # 设置合计行样式
    for col in range(1, 14):
        cell = ws.cell(row=row_num, column=col)
        cell.font = Font(bold=True)
        if col == 1 or col == 8:
            cell.alignment = Alignment(horizontal='center')
    
    # 设置数据行样式
    for row in range(2, row_num):
        for col in range(1, 14):
            cell = ws.cell(row=row, column=col)
            cell.alignment = Alignment(horizontal='center' if col in [1, 7] else 'left')
            if col in [5, 6, 8]:  # 数值列右对齐
                cell.alignment = Alignment(horizontal='right')
    
    # 保存文件
    output_file = "2025年1月居间结算单明细.xlsx"
    wb.save(output_file)
    
    print(f"转换完成！")
    print(f"输出文件: {output_file}")
    print(f"处理记录数: {len(df_source)}")
    print(f"总居间费用: {total_settlement_fee:.2f}元")
    
    return output_file

def extract_rate_from_text(text):
    """从文本中提取费率"""
    if pd.isna(text) or text == '':
        return 0.003  # 默认3厘
    
    text = str(text).lower()
    
    # 查找百分比
    percent_match = re.search(r'(\d+(?:\.\d+)?)%', text)
    if percent_match:
        return float(percent_match.group(1)) / 100
    
    # 查找厘
    li_match = re.search(r'(\d+(?:\.\d+)?)厘', text)
    if li_match:
        return float(li_match.group(1)) / 1000
    
    # 查找分成比例
    if '分成' in text:
        ratio_match = re.search(r'(\d+):(\d+)', text)
        if ratio_match:
            a, b = int(ratio_match.group(1)), int(ratio_match.group(2))
            return a / (a + b)
    
    return 0.003  # 默认3厘

if __name__ == "__main__":
    create_settlement_report()
