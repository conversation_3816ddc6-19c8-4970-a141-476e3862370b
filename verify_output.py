#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl

def verify_output():
    """验证输出文件的内容"""
    print("=== 验证输出文件 ===")
    
    try:
        # 读取生成的文件
        df_output = pd.read_excel("2025年1月居间结算单明细.xlsx")
        print(f"输出文件读取成功: {df_output.shape}")
        print(f"列名: {list(df_output.columns)}")
        
        # 显示前5行数据
        print("\n前5行数据:")
        print(df_output.head())
        
        # 统计信息
        print(f"\n统计信息:")
        print(f"总记录数: {len(df_output)}")
        print(f"总结算电量: {df_output['结算电量(kWh)'].sum():,.0f} kWh")
        print(f"总交易电费: {df_output['交易电费(元)'].sum():,.2f} 元")
        print(f"总居间费用: {df_output['居间费用(元)'].sum():,.2f} 元")
        
        # 检查数据完整性
        print(f"\n数据完整性检查:")
        for col in df_output.columns:
            null_count = df_output[col].isnull().sum()
            if null_count > 0:
                print(f"  {col}: {null_count} 个空值")
        
        # 检查是否有匹配到联系信息的记录
        matched_contacts = df_output[df_output['联系人'].notna() & (df_output['联系人'] != '')].shape[0]
        matched_phones = df_output[df_output['联系电话'].notna() & (df_output['联系电话'] != '')].shape[0]
        matched_banks = df_output[df_output['开户行'].notna() & (df_output['开户行'] != '')].shape[0]
        
        print(f"\n匹配情况:")
        print(f"  有联系人信息的记录: {matched_contacts}/{len(df_output)}")
        print(f"  有联系电话的记录: {matched_phones}/{len(df_output)}")
        print(f"  有开户行信息的记录: {matched_banks}/{len(df_output)}")
        
        # 显示居间费率分布
        print(f"\n居间费率分布:")
        rate_counts = df_output['居间费率'].value_counts()
        for rate, count in rate_counts.head(10).items():
            print(f"  {rate}: {count} 条记录")
            
    except Exception as e:
        print(f"验证过程出错: {e}")

def compare_with_source():
    """与源数据进行对比验证"""
    print("\n=== 与源数据对比 ===")
    
    try:
        # 读取源数据和输出数据
        df_source = pd.read_excel("零售套餐用户量费明细 (7).xlsx")
        df_output = pd.read_excel("2025年1月居间结算单明细.xlsx")
        
        print(f"源数据记录数: {len(df_source)}")
        print(f"输出数据记录数: {len(df_output)}")
        
        # 对比总金额
        source_total_fee = df_source['交易电费'].sum()
        output_total_fee = df_output['交易电费(元)'].sum()
        
        print(f"\n交易电费对比:")
        print(f"  源数据总交易电费: {source_total_fee:,.2f} 元")
        print(f"  输出数据总交易电费: {output_total_fee:,.2f} 元")
        print(f"  差异: {abs(source_total_fee - output_total_fee):,.2f} 元")
        
        # 对比结算电量
        source_total_amount = df_source['结算电量（总）'].sum()
        output_total_amount = df_output['结算电量(kWh)'].sum()
        
        print(f"\n结算电量对比:")
        print(f"  源数据总结算电量: {source_total_amount:,.0f} kWh")
        print(f"  输出数据总结算电量: {output_total_amount:,.0f} kWh")
        print(f"  差异: {abs(source_total_amount - output_total_amount):,.0f} kWh")
        
        # 检查企业名称匹配
        source_companies = set(df_source['用户名称'].unique())
        output_companies = set(df_output['企业名称'].unique())
        
        print(f"\n企业名称匹配:")
        print(f"  源数据企业数: {len(source_companies)}")
        print(f"  输出数据企业数: {len(output_companies)}")
        print(f"  完全匹配: {len(source_companies & output_companies)}")
        
        if source_companies != output_companies:
            missing = source_companies - output_companies
            extra = output_companies - source_companies
            if missing:
                print(f"  缺失企业: {list(missing)[:5]}...")
            if extra:
                print(f"  多余企业: {list(extra)[:5]}...")
        
    except Exception as e:
        print(f"对比过程出错: {e}")

def show_sample_records():
    """显示一些样本记录"""
    print("\n=== 样本记录展示 ===")
    
    try:
        df_output = pd.read_excel("2025年1月居间结算单明细.xlsx")
        
        # 显示居间费用最高的5条记录
        print("居间费用最高的5条记录:")
        top_records = df_output.nlargest(5, '居间费用(元)')
        for _, row in top_records.iterrows():
            print(f"  {row['企业名称']}: {row['居间费用(元)']:.2f}元 (费率: {row['居间费率']})")
        
        # 显示有完整联系信息的记录
        complete_info = df_output[
            (df_output['联系人'].notna()) & 
            (df_output['联系电话'].notna()) & 
            (df_output['开户行'].notna()) &
            (df_output['联系人'] != '') &
            (df_output['联系电话'] != '') &
            (df_output['开户行'] != '')
        ]
        
        print(f"\n有完整联系信息的记录数: {len(complete_info)}")
        if len(complete_info) > 0:
            print("样本记录:")
            for _, row in complete_info.head(3).iterrows():
                print(f"  {row['企业名称']}: {row['联系人']} - {row['联系电话']}")
        
    except Exception as e:
        print(f"样本展示出错: {e}")

if __name__ == "__main__":
    verify_output()
    compare_with_source()
    show_sample_records()
