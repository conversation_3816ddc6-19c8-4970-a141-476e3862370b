#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def analyze_files():
    """分析所有文件的结构"""
    print("=== 文件分析 ===")
    
    # 1. 分析模板文件
    template_file = "副本模板-2025年1月居间结算单明细(1).xlsx"
    print(f"\n1. 模板文件: {template_file}")
    try:
        # 尝试不同方法读取模板
        wb_template = openpyxl.load_workbook(template_file)
        print(f"   工作表: {wb_template.sheetnames}")
        
        for sheet_name in wb_template.sheetnames:
            ws = wb_template[sheet_name]
            print(f"   工作表 '{sheet_name}': {ws.max_row}行 x {ws.max_column}列")
            
            # 显示前几行的结构
            print("   前5行结构:")
            for row in range(1, min(6, ws.max_row + 1)):
                row_data = []
                for col in range(1, min(ws.max_column + 1, 10)):
                    cell = ws.cell(row=row, column=col)
                    row_data.append(str(cell.value)[:20] if cell.value else "")
                print(f"     第{row}行: {row_data}")
    except Exception as e:
        print(f"   读取模板失败: {e}")
        # 尝试用pandas读取
        try:
            df_template = pd.read_excel(template_file, engine='openpyxl')
            print(f"   用pandas读取成功: {df_template.shape}")
            print(f"   列名: {list(df_template.columns)[:10]}")
        except Exception as e2:
            print(f"   pandas也失败: {e2}")
    
    # 2. 分析源数据文件
    source_file = "零售套餐用户量费明细 (7).xlsx"
    print(f"\n2. 源数据文件: {source_file}")
    try:
        df_source = pd.read_excel(source_file)
        print(f"   形状: {df_source.shape}")
        print(f"   列名: {list(df_source.columns)}")
        print(f"   年月范围: {df_source['年月'].unique()}")
        print(f"   用户数量: {df_source['用户名称'].nunique()}")
    except Exception as e:
        print(f"   读取源数据失败: {e}")
    
    # 3. 分析工作簿3.xlsx的所有工作表
    ref_file = "工作簿3.xlsx"
    print(f"\n3. 参考数据文件: {ref_file}")
    try:
        wb_ref = openpyxl.load_workbook(ref_file)
        print(f"   包含工作表: {wb_ref.sheetnames}")
        
        # 分析每个工作表
        for sheet_name in wb_ref.sheetnames:
            print(f"\n   工作表: {sheet_name}")
            try:
                df = pd.read_excel(ref_file, sheet_name=sheet_name)
                print(f"     形状: {df.shape}")
                print(f"     列名: {list(df.columns)[:8]}")  # 只显示前8列
                
                # 显示前2行数据
                if not df.empty:
                    print("     前2行数据:")
                    for i, row in df.head(2).iterrows():
                        print(f"       第{i+1}行: {dict(list(row.items())[:5])}")  # 只显示前5个字段
                        
            except Exception as e:
                print(f"     读取工作表失败: {e}")
                
    except Exception as e:
        print(f"   读取参考文件失败: {e}")

def create_template_based_output():
    """基于模板创建输出文件"""
    print("\n=== 开始转换 ===")
    
    try:
        # 读取源数据
        df_source = pd.read_excel("零售套餐用户量费明细 (7).xlsx")
        print(f"源数据读取成功: {df_source.shape}")
        
        # 读取参考数据 - 商务居间表
        df_business = pd.read_excel("工作簿3.xlsx", sheet_name='商务居间')
        print(f"商务居间数据读取成功: {df_business.shape}")
        
        # 读取参考数据 - 浙江绿电结算信息表
        df_settlement = pd.read_excel("工作簿3.xlsx", sheet_name='浙江绿电结算信息')
        print(f"浙江绿电结算信息读取成功: {df_settlement.shape}")
        
        # 创建新的工作簿
        wb_output = openpyxl.Workbook()
        ws = wb_output.active
        ws.title = "2025年1月居间结算单明细"
        
        # 设置表头
        headers = [
            "序号", "企业名称", "用户编号", "供电单位", "结算电量（总）", 
            "交易电费", "居间费率", "居间费用", "联系人", "联系电话",
            "开户行", "银行账号", "备注"
        ]
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 处理数据
        row_num = 2
        for idx, source_row in df_source.iterrows():
            ws.cell(row=row_num, column=1, value=row_num-1)  # 序号
            ws.cell(row=row_num, column=2, value=source_row['用户名称'])  # 企业名称
            ws.cell(row=row_num, column=3, value=source_row['用户编号'])  # 用户编号
            ws.cell(row=row_num, column=4, value=source_row['供电单位'])  # 供电单位
            ws.cell(row=row_num, column=5, value=source_row['结算电量（总）'])  # 结算电量
            ws.cell(row=row_num, column=6, value=source_row['交易电费'])  # 交易电费
            
            # 从商务居间表匹配居间费率（这里需要根据实际匹配逻辑调整）
            # 暂时设置默认值
            ws.cell(row=row_num, column=7, value="待匹配")  # 居间费率
            ws.cell(row=row_num, column=8, value="待计算")  # 居间费用
            ws.cell(row=row_num, column=9, value="待匹配")  # 联系人
            ws.cell(row=row_num, column=10, value="待匹配")  # 联系电话
            ws.cell(row=row_num, column=11, value="待匹配")  # 开户行
            ws.cell(row=row_num, column=12, value="待匹配")  # 银行账号
            ws.cell(row=row_num, column=13, value="")  # 备注
            
            row_num += 1
        
        # 保存文件
        output_file = "转换后的居间结算单明细.xlsx"
        wb_output.save(output_file)
        print(f"转换完成，输出文件: {output_file}")
        
    except Exception as e:
        print(f"转换过程出错: {e}")

def main():
    analyze_files()
    
    # 询问是否继续转换
    print("\n是否继续进行数据转换？(y/n)")
    # 由于这是脚本，我们直接进行转换
    create_template_based_output()

if __name__ == "__main__":
    main()
