#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
import sys
import os

def analyze_template_file():
    """专门分析模板文件"""
    template_file = "副本模板-2025年1月居间结算单明细(1).xlsx"
    print(f"=== 分析模板文件: {template_file} ===")
    
    if not os.path.exists(template_file):
        print(f"文件不存在: {template_file}")
        return
    
    # 尝试多种方法读取模板文件
    methods = [
        ('openpyxl直接读取', lambda: openpyxl.load_workbook(template_file)),
        ('pandas+openpyxl', lambda: pd.read_excel(template_file, engine='openpyxl')),
        ('pandas默认', lambda: pd.read_excel(template_file)),
    ]
    
    for method_name, method_func in methods:
        try:
            print(f"\n--- 尝试方法: {method_name} ---")
            if 'openpyxl直接' in method_name:
                wb = method_func()
                print(f"工作表名称: {wb.sheetnames}")
                for sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                    print(f"工作表 {sheet_name}: 最大行={ws.max_row}, 最大列={ws.max_column}")
                    # 读取前几行数据
                    for row in range(1, min(6, ws.max_row + 1)):
                        row_data = []
                        for col in range(1, min(11, ws.max_column + 1)):
                            cell_value = ws.cell(row=row, column=col).value
                            row_data.append(cell_value)
                        print(f"第{row}行: {row_data}")
            else:
                df = method_func()
                print(f"成功读取，形状: {df.shape}")
                print(f"列名: {list(df.columns)}")
                print("前5行数据:")
                print(df.head())
            break
        except Exception as e:
            print(f"{method_name} 失败: {e}")
            continue
    
    print("\n" + "="*60)

def analyze_source_data():
    """分析源数据文件"""
    source_file = "零售套餐用户量费明细 (7).xlsx"
    print(f"=== 分析源数据文件: {source_file} ===")
    
    try:
        df = pd.read_excel(source_file)
        print(f"形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("前3行数据:")
        print(df.head(3))
        print("\n关键字段统计:")
        print(f"年月唯一值: {df['年月'].unique()}")
        print(f"用户名称数量: {df['用户名称'].nunique()}")
        print(f"供电单位唯一值: {df['供电单位'].unique()}")
    except Exception as e:
        print(f"读取源数据失败: {e}")

def analyze_reference_data():
    """分析参考数据文件"""
    ref_file = "工作簿3.xlsx"
    print(f"=== 分析参考数据文件: {ref_file} ===")
    
    try:
        # 读取商务居间工作表
        df_business = pd.read_excel(ref_file, sheet_name='商务居间')
        print("商务居间工作表:")
        print(f"形状: {df_business.shape}")
        print(f"列名: {list(df_business.columns)}")
        print("前3行数据:")
        print(df_business.head(3))
        
        # 读取浙江绿电结算信息工作表
        df_settlement = pd.read_excel(ref_file, sheet_name='浙江绿电结算信息')
        print("\n浙江绿电结算信息工作表:")
        print(f"形状: {df_settlement.shape}")
        print(f"列名: {list(df_settlement.columns)}")
        print("前3行数据:")
        print(df_settlement.head(3))
        
    except Exception as e:
        print(f"读取参考数据失败: {e}")

def main():
    analyze_template_file()
    analyze_source_data()
    analyze_reference_data()

if __name__ == "__main__":
    main()
