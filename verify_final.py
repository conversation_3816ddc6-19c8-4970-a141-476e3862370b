#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_final_output():
    """验证最终输出文件"""
    print("=== 验证最终输出文件 ===")
    
    try:
        # 读取最终输出文件
        df_final = pd.read_excel("2025年1月居间结算单明细-最终版.xlsx")
        print(f"最终输出文件读取成功: {df_final.shape}")
        
        # 显示基本信息
        print(f"\n基本信息:")
        print(f"总记录数: {len(df_final)}")
        print(f"列名: {list(df_final.columns)}")
        
        # 显示前3行数据
        print(f"\n前3行数据:")
        for i, row in df_final.head(3).iterrows():
            print(f"第{i+1}行:")
            print(f"  企业名称: {row['企业名称']}")
            print(f"  用户编号: {row['用户编号']}")
            print(f"  结算电量: {row['结算电量(kWh)']} kWh")
            print(f"  交易电费: {row['交易电费(元)']} 元")
            print(f"  居间费率: {row['居间费率']}")
            print(f"  居间费用: {row['居间费用(元)']} 元")
            print(f"  联系人: {row['联系人']}")
            print(f"  联系电话: {row['联系电话']}")
            print()
        
        # 统计信息
        print(f"统计信息:")
        print(f"总结算电量: {df_final['结算电量(kWh)'].sum():,.0f} kWh")
        print(f"总交易电费: {df_final['交易电费(元)'].sum():,.2f} 元")
        print(f"总居间费用: {df_final['居间费用(元)'].sum():,.2f} 元")
        
        # 检查联系信息完整性
        has_contact = df_final[df_final['联系人'].notna() & (df_final['联系人'] != '')].shape[0]
        has_phone = df_final[df_final['联系电话'].notna() & (df_final['联系电话'] != '')].shape[0]
        has_bank = df_final[df_final['开户行'].notna() & (df_final['开户行'] != '')].shape[0]
        has_account = df_final[df_final['银行账号'].notna() & (df_final['银行账号'] != '')].shape[0]
        
        print(f"\n联系信息完整性:")
        print(f"有联系人: {has_contact}/{len(df_final)} ({has_contact/len(df_final)*100:.1f}%)")
        print(f"有联系电话: {has_phone}/{len(df_final)} ({has_phone/len(df_final)*100:.1f}%)")
        print(f"有开户行: {has_bank}/{len(df_final)} ({has_bank/len(df_final)*100:.1f}%)")
        print(f"有银行账号: {has_account}/{len(df_final)} ({has_account/len(df_final)*100:.1f}%)")
        
        # 居间费率分布
        print(f"\n居间费率分布:")
        rate_counts = df_final['居间费率'].value_counts()
        for rate, count in rate_counts.items():
            print(f"  {rate}: {count} 条记录")
        
        # 显示居间费用最高的企业
        print(f"\n居间费用最高的10家企业:")
        top_companies = df_final.nlargest(10, '居间费用(元)')
        for i, (_, row) in enumerate(top_companies.iterrows(), 1):
            print(f"  {i}. {row['企业名称']}: {row['居间费用(元)']:.2f}元")
        
        # 检查是否有合计行
        last_row = df_final.iloc[-1]
        if str(last_row['序号']).strip() == '合计':
            print(f"\n合计行信息:")
            print(f"  总交易电费: {last_row['交易电费(元)']} 元")
            print(f"  总居间费用: {last_row['居间费用(元)']} 元")
        
        # 显示有完整信息的样本记录
        complete_records = df_final[
            (df_final['联系人'].notna()) & 
            (df_final['联系电话'].notna()) & 
            (df_final['开户行'].notna()) &
            (df_final['银行账号'].notna()) &
            (df_final['联系人'] != '') &
            (df_final['联系电话'] != '') &
            (df_final['开户行'] != '') &
            (df_final['银行账号'] != '')
        ]
        
        print(f"\n有完整信息的记录样本 ({len(complete_records)} 条):")
        for i, (_, row) in enumerate(complete_records.head(5).iterrows(), 1):
            print(f"  {i}. {row['企业名称']}")
            print(f"     联系人: {row['联系人']}")
            print(f"     电话: {row['联系电话']}")
            print(f"     开户行: {row['开户行']}")
            print(f"     账号: {row['银行账号']}")
            print()
        
    except Exception as e:
        print(f"验证过程出错: {e}")

def compare_versions():
    """对比不同版本的输出"""
    print("=== 对比不同版本 ===")
    
    try:
        # 读取两个版本
        df_v1 = pd.read_excel("2025年1月居间结算单明细.xlsx")
        df_v2 = pd.read_excel("2025年1月居间结算单明细-最终版.xlsx")
        
        print(f"第一版记录数: {len(df_v1)}")
        print(f"最终版记录数: {len(df_v2)}")
        
        # 对比联系信息匹配率
        v1_contacts = df_v1[df_v1['联系人'].notna() & (df_v1['联系人'] != '')].shape[0]
        v2_contacts = df_v2[df_v2['联系人'].notna() & (df_v2['联系人'] != '')].shape[0]
        
        print(f"\n联系信息匹配对比:")
        print(f"第一版有联系人: {v1_contacts}/{len(df_v1)} ({v1_contacts/len(df_v1)*100:.1f}%)")
        print(f"最终版有联系人: {v2_contacts}/{len(df_v2)} ({v2_contacts/len(df_v2)*100:.1f}%)")
        print(f"改进: +{v2_contacts - v1_contacts} 条记录")
        
    except Exception as e:
        print(f"对比过程出错: {e}")

if __name__ == "__main__":
    verify_final_output()
    compare_versions()
