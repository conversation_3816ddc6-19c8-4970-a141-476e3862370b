#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
import sys

def analyze_excel_file(file_path):
    """分析Excel文件的结构和内容"""
    print(f"\n=== 分析文件: {file_path} ===")
    
    try:
        # 使用openpyxl读取工作表名称
        wb = openpyxl.load_workbook(file_path)
        print(f"工作表名称: {wb.sheetnames}")
        
        # 使用pandas读取每个工作表
        for sheet_name in wb.sheetnames:
            print(f"\n--- 工作表: {sheet_name} ---")
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                print(f"形状: {df.shape}")
                print(f"列名: {list(df.columns)}")
                print("前5行数据:")
                print(df.head())
                print("\n数据类型:")
                print(df.dtypes)
            except Exception as e:
                print(f"读取工作表 {sheet_name} 时出错: {e}")
                
    except Exception as e:
        print(f"读取文件时出错: {e}")

def main():
    files = [
        "副本模板-2025年1月居间结算单明细(1).xlsx",  # 模板文件
        "零售套餐用户量费明细 (7).xlsx",              # 需要转换的文件
        "工作簿3.xlsx"                              # 匹配数据文件
    ]
    
    for file_path in files:
        analyze_excel_file(file_path)
        print("\n" + "="*80)

if __name__ == "__main__":
    main()
