#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import re
from fuzzywuzzy import fuzz

def load_template_structure():
    """加载模板结构"""
    print("=== 加载模板结构 ===")
    
    # 使用xlrd引擎读取模板
    df_template = pd.read_excel("副本模板-2025年1月居间结算单明细(1).xlsx", engine='xlrd')
    print(f"模板结构: {df_template.shape}")
    print(f"模板列名: {list(df_template.columns)}")
    
    # 显示模板前几行作为参考
    print("模板前3行数据:")
    print(df_template.head(3))
    
    return df_template

def create_settlement_report_with_template():
    """按照模板结构创建居间结算单"""
    print("\n=== 按模板结构创建居间结算单 ===")
    
    # 加载模板结构
    df_template = load_template_structure()
    
    # 加载源数据
    df_source = pd.read_excel("零售套餐用户量费明细 (7).xlsx")
    df_business = pd.read_excel("工作簿3.xlsx", sheet_name='商务居间')
    df_settlement = pd.read_excel("工作簿3.xlsx", sheet_name='浙江绿电结算信息')
    
    print(f"源数据: {df_source.shape}")
    print(f"商务居间: {df_business.shape}")
    print(f"绿电结算: {df_settlement.shape}")
    
    # 创建企业信息映射
    print("创建企业信息映射...")
    
    # 绿电结算信息映射
    settlement_map = {}
    for _, row in df_settlement.iterrows():
        if pd.notna(row['企业名称']):
            settlement_map[row['企业名称']] = {
                '联系人': row.get('联系人', ''),
                '联系电话': str(row.get('联系人手机', '')).replace('.0', '') if pd.notna(row.get('联系人手机')) else '',
                '开户行': row.get('开户行', ''),
                '银行账号': str(row.get('银行账号', '')).replace('.0', '') if pd.notna(row.get('银行账号')) else '',
                '结算方式': row.get('结算方式', '')
            }
    
    # 商务居间映射
    business_map = {}
    for _, row in df_business.iterrows():
        if pd.notna(row['企业名称/个人名称']):
            price_info = str(row.get('价格', ''))
            rate = extract_rate_from_text(price_info)
            
            business_map[row['企业名称/个人名称']] = {
                '居间费率': rate,
                '联系人': row.get('联系人', ''),
                '联系电话': str(row.get('联系人手机', '')).replace('.0', '') if pd.notna(row.get('联系人手机')) else '',
                '开户行': row.get('开户行', ''),
                '银行账号': str(row.get('银行账号', '')).replace('.0', '') if pd.notna(row.get('银行账号')) else '',
                '结算方式': row.get('结算方式', '')
            }
    
    # 创建新的DataFrame，按照模板结构
    result_data = []
    
    print("开始数据转换...")
    for idx, source_row in df_source.iterrows():
        company_name = source_row['用户名称']
        settlement_amount_kwh = float(source_row['结算电量（总）']) if pd.notna(source_row['结算电量（总）']) else 0
        settlement_amount_mwh = settlement_amount_kwh / 1000  # 转换为兆瓦时
        trading_fee = float(source_row['交易电费']) if pd.notna(source_row['交易电费']) else 0
        
        # 查找匹配的企业信息
        settlement_info = settlement_map.get(company_name, {})
        business_info = business_map.get(company_name, {})
        
        # 尝试模糊匹配
        if not settlement_info and not business_info:
            settlement_companies = list(settlement_map.keys())
            business_companies = list(business_map.keys())
            
            fuzzy_settlement = fuzzy_match_company(company_name, settlement_companies)
            if fuzzy_settlement:
                settlement_info = settlement_map.get(fuzzy_settlement, {})
            
            fuzzy_business = fuzzy_match_company(company_name, business_companies)
            if fuzzy_business:
                business_info = business_map.get(fuzzy_business, {})
        
        # 确定居间费率和费用
        rate = business_info.get('居间费率', 0.003)  # 默认3厘
        settlement_fee_before_tax = trading_fee * rate
        
        # 税率计算（假设6%增值税）
        tax_rate = 0.06
        settlement_fee_after_tax = settlement_fee_before_tax * (1 + tax_rate)
        
        # 优先使用结算信息，其次使用商务居间信息
        contact_info = settlement_info if settlement_info else business_info
        
        # 按照模板结构创建行数据
        row_data = {
            '居间人/单位': contact_info.get('联系人', ''),  # 居间人
            '序号': idx + 1,  # 序号
            '企业用户名': company_name,  # 企业用户名
            '结算电量（兆瓦时）': round(settlement_amount_mwh, 3),  # 结算电量（兆瓦时）
            '单价（厘）': rate * 1000,  # 单价（厘）
            '税前费用': round(settlement_fee_before_tax, 2),  # 税前费用
            '税前合计（元）': round(settlement_fee_before_tax, 2),  # 税前合计
            '发票': '普通发票',  # 发票类型
            '税后合计（元）': round(settlement_fee_after_tax, 2),  # 税后合计
            '开户行': contact_info.get('开户行', ''),  # 开户行
            '收款人/单位': contact_info.get('联系人', ''),  # 收款人/单位
            '银行账号': contact_info.get('银行账号', '')  # 银行账号
        }
        
        result_data.append(row_data)
    
    # 创建DataFrame
    df_result = pd.DataFrame(result_data)
    
    # 创建Excel文件
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "2025年1月居间结算单明细"
    
    # 设置列宽
    column_widths = [15, 8, 25, 15, 12, 15, 15, 12, 15, 25, 20, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width
    
    # 写入数据
    for r in dataframe_to_rows(df_result, index=False, header=True):
        ws.append(r)
    
    # 设置表头样式
    header_font = Font(name='宋体', size=11, bold=True)
    header_alignment = Alignment(horizontal='center', vertical='center')
    header_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    # 应用表头样式
    for col in range(1, len(df_result.columns) + 1):
        cell = ws.cell(row=1, column=col)
        cell.font = header_font
        cell.alignment = header_alignment
        cell.fill = header_fill
        cell.border = thin_border
    
    # 设置数据行样式
    for row in range(2, len(df_result) + 2):
        for col in range(1, len(df_result.columns) + 1):
            cell = ws.cell(row=row, column=col)
            cell.border = thin_border
            
            # 根据列类型设置对齐方式
            if col in [2]:  # 序号居中
                cell.alignment = Alignment(horizontal='center')
            elif col in [4, 5, 6, 7, 9]:  # 数值列右对齐
                cell.alignment = Alignment(horizontal='right')
            else:
                cell.alignment = Alignment(horizontal='left')
    
    # 添加合计行
    total_row = len(df_result) + 2
    ws.cell(row=total_row, column=1, value="合计")
    ws.cell(row=total_row, column=6, value=df_result['税前费用'].sum())
    ws.cell(row=total_row, column=7, value=df_result['税前合计（元）'].sum())
    ws.cell(row=total_row, column=9, value=df_result['税后合计（元）'].sum())
    
    # 设置合计行样式
    for col in range(1, len(df_result.columns) + 1):
        cell = ws.cell(row=total_row, column=col)
        cell.font = Font(bold=True)
        cell.border = thin_border
        cell.fill = PatternFill(start_color='E6E6FA', end_color='E6E6FA', fill_type='solid')
        if col == 1:
            cell.alignment = Alignment(horizontal='center')
        elif col in [6, 7, 9]:
            cell.alignment = Alignment(horizontal='right')
    
    # 保存文件
    output_file = "按模板结构-2025年1月居间结算单明细.xlsx"
    wb.save(output_file)
    
    print(f"\n转换完成！")
    print(f"输出文件: {output_file}")
    print(f"处理记录数: {len(df_result)}")
    print(f"税前合计: {df_result['税前合计（元）'].sum():.2f}元")
    print(f"税后合计: {df_result['税后合计（元）'].sum():.2f}元")
    
    return output_file, df_result

def fuzzy_match_company(company_name, reference_companies, threshold=80):
    """使用模糊匹配查找最相似的公司名称"""
    if pd.isna(company_name):
        return None
    
    best_match = None
    best_score = 0
    
    for ref_company in reference_companies:
        if pd.isna(ref_company):
            continue
        score = fuzz.ratio(str(company_name), str(ref_company))
        if score > best_score and score >= threshold:
            best_score = score
            best_match = ref_company
    
    return best_match

def extract_rate_from_text(text):
    """从文本中提取费率"""
    if pd.isna(text) or text == '':
        return 0.003
    
    text = str(text).lower()
    
    # 查找百分比
    percent_match = re.search(r'(\d+(?:\.\d+)?)%', text)
    if percent_match:
        return float(percent_match.group(1)) / 100
    
    # 查找厘
    li_match = re.search(r'(\d+(?:\.\d+)?)厘', text)
    if li_match:
        return float(li_match.group(1)) / 1000
    
    # 查找分成比例
    if '分成' in text:
        ratio_match = re.search(r'(\d+):(\d+)', text)
        if ratio_match:
            a, b = int(ratio_match.group(1)), int(ratio_match.group(2))
            return a / (a + b)
    
    return 0.003

if __name__ == "__main__":
    create_settlement_report_with_template()
