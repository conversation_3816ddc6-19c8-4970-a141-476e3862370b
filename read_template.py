#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
import xlrd
from openpyxl import load_workbook
import os

def try_read_template():
    """尝试用各种方法读取模板文件"""
    template_file = "副本模板-2025年1月居间结算单明细(1).xlsx"
    
    print(f"=== 尝试读取模板文件: {template_file} ===")
    print(f"文件大小: {os.path.getsize(template_file)} bytes")
    
    methods = [
        ("pandas + openpyxl引擎", lambda: pd.read_excel(template_file, engine='openpyxl')),
        ("pandas + xlrd引擎", lambda: pd.read_excel(template_file, engine='xlrd')),
        ("pandas 默认", lambda: pd.read_excel(template_file)),
        ("openpyxl直接读取", read_with_openpyxl),
        ("尝试修复后读取", read_with_repair),
    ]
    
    for method_name, method_func in methods:
        print(f"\n--- {method_name} ---")
        try:
            if "openpyxl直接" in method_name:
                result = method_func(template_file)
            elif "修复" in method_name:
                result = method_func(template_file)
            else:
                result = method_func()
            
            if isinstance(result, pd.DataFrame):
                print(f"成功读取DataFrame: {result.shape}")
                print(f"列名: {list(result.columns)}")
                print("前3行数据:")
                print(result.head(3))
                return result, "DataFrame"
            else:
                print("成功读取工作簿")
                return result, "Workbook"
                
        except Exception as e:
            print(f"失败: {e}")
    
    return None, None

def read_with_openpyxl(file_path):
    """使用openpyxl直接读取"""
    wb = load_workbook(file_path, data_only=True)
    print(f"工作表: {wb.sheetnames}")
    
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        print(f"\n工作表 '{sheet_name}': {ws.max_row}行 x {ws.max_column}列")
        
        # 读取所有数据
        data = []
        for row in ws.iter_rows(values_only=True):
            data.append(row)
        
        # 显示前10行
        print("前10行数据:")
        for i, row in enumerate(data[:10]):
            print(f"第{i+1}行: {row}")
        
        return wb

def read_with_repair(file_path):
    """尝试修复后读取"""
    # 尝试忽略错误读取
    try:
        wb = load_workbook(file_path, data_only=True, read_only=True)
        return wb
    except Exception as e1:
        print(f"只读模式失败: {e1}")
        
        # 尝试用pandas忽略错误
        try:
            df = pd.read_excel(file_path, engine='openpyxl', header=None)
            return df
        except Exception as e2:
            print(f"pandas忽略错误也失败: {e2}")
            return None

def create_template_structure():
    """如果无法读取模板，手动创建标准的居间结算单结构"""
    print("\n=== 创建标准居间结算单结构 ===")
    
    # 基于常见的居间结算单格式创建结构
    template_structure = {
        'title': '2025年1月居间结算单明细',
        'headers': [
            '序号', '企业名称', '用户编号', '供电单位', '结算电量(kWh)', 
            '交易电费(元)', '居间费率', '居间费用(元)', '联系人', '联系电话',
            '开户行', '银行账号', '备注'
        ],
        'title_row': 1,
        'header_row': 2,
        'data_start_row': 3
    }
    
    return template_structure

def analyze_existing_output():
    """分析现有输出文件的结构作为参考"""
    print("\n=== 分析现有输出文件结构 ===")
    
    try:
        df = pd.read_excel("2025年1月居间结算单明细-最终版.xlsx")
        print(f"现有文件结构: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 用openpyxl读取以查看格式
        wb = load_workbook("2025年1月居间结算单明细-最终版.xlsx")
        ws = wb.active
        
        print(f"工作表名: {ws.title}")
        print(f"最大行: {ws.max_row}, 最大列: {ws.max_column}")
        
        # 检查第一行是否是标题
        first_row = [cell.value for cell in ws[1]]
        print(f"第一行: {first_row}")
        
        return {
            'dataframe': df,
            'workbook': wb,
            'structure': {
                'title': ws.title,
                'headers': first_row,
                'data_rows': ws.max_row - 1
            }
        }
        
    except Exception as e:
        print(f"分析现有文件失败: {e}")
        return None

def main():
    # 尝试读取模板
    template_data, data_type = try_read_template()
    
    if template_data is None:
        print("\n无法读取模板文件，将使用标准结构")
        template_structure = create_template_structure()
        print(f"使用标准结构: {template_structure}")
    else:
        print(f"\n成功读取模板，数据类型: {data_type}")
    
    # 分析现有输出作为参考
    existing_analysis = analyze_existing_output()
    
    print("\n=== 建议 ===")
    print("1. 如果模板文件损坏，建议提供一个可读的模板文件")
    print("2. 或者明确说明需要的具体格式要求")
    print("3. 当前生成的文件已经包含了标准的居间结算单格式")

if __name__ == "__main__":
    main()
